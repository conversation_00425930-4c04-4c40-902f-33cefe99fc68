#!/usr/bin/env python3
"""
初始化管理员账户脚本
运行此脚本创建默认管理员账户
"""

from auth import UserManager
from points_system import PointsSystem

def create_admin_user():
    """创建管理员用户"""
    user_manager = UserManager()
    points_system = PointsSystem()
    
    # 默认管理员信息
    admin_username = "admin"
    admin_password = "admin123"
    admin_email = "<EMAIL>"
    
    print("正在创建管理员账户...")
    
    # 检查管理员是否已存在
    if admin_username in user_manager.users:
        print(f"管理员账户 '{admin_username}' 已存在")
        
        # 确保是管理员权限
        user = user_manager.users[admin_username]
        if not user.get('is_admin', False):
            user['is_admin'] = True
            user_manager.save_users()
            print(f"已将用户 '{admin_username}' 设置为管理员")
        
        return True
    
    # 创建管理员账户
    success, message = user_manager.register_user(admin_username, admin_password, admin_email)
    
    if success:
        # 设置为管理员
        user = user_manager.users[admin_username]
        user['is_admin'] = True
        user['points'] = 1000  # 给管理员更多积分
        user_manager.save_users()
        
        # 记录管理员创建
        points_system.admin_adjust_points(admin_username, 990, "管理员账户初始化")
        
        print(f"管理员账户创建成功！")
        print(f"用户名: {admin_username}")
        print(f"密码: {admin_password}")
        print(f"邮箱: {admin_email}")
        print(f"初始积分: 1000")
        print("\n⚠️  请及时修改默认密码！")
        
        return True
    else:
        print(f"创建管理员账户失败: {message}")
        return False

def create_test_users():
    """创建一些测试用户"""
    user_manager = UserManager()
    points_system = PointsSystem()
    
    test_users = [
        {"username": "user1", "password": "123456", "email": "<EMAIL>"},
        {"username": "user2", "password": "123456", "email": "<EMAIL>"},
        {"username": "user3", "password": "123456", "email": "<EMAIL>"},
    ]
    
    print("\n正在创建测试用户...")
    
    for user_info in test_users:
        username = user_info["username"]
        
        if username in user_manager.users:
            print(f"测试用户 '{username}' 已存在，跳过")
            continue
        
        success, message = user_manager.register_user(
            username, 
            user_info["password"], 
            user_info["email"]
        )
        
        if success:
            print(f"创建测试用户 '{username}' 成功")
        else:
            print(f"创建测试用户 '{username}' 失败: {message}")

def display_system_info():
    """显示系统信息"""
    user_manager = UserManager()
    points_system = PointsSystem()
    
    print("\n" + "="*50)
    print("系统信息")
    print("="*50)
    
    # 用户统计
    total_users = len(user_manager.users)
    admin_users = sum(1 for user in user_manager.users.values() if user.get('is_admin', False))
    
    print(f"总用户数: {total_users}")
    print(f"管理员数: {admin_users}")
    
    # 积分统计
    stats = points_system.get_system_statistics()
    print(f"总交易数: {stats['total_transactions']}")
    print(f"总发放积分: {stats['total_points_issued']}")
    print(f"总消耗积分: {stats['total_points_consumed']}")
    print(f"总生成次数: {stats['total_generations']}")
    
    # 用户列表
    print("\n用户列表:")
    print("-" * 80)
    print(f"{'用户名':<15} {'积分':<8} {'生成次数':<10} {'管理员':<8} {'注册时间'}")
    print("-" * 80)
    
    for username, user_data in user_manager.users.items():
        is_admin = "是" if user_data.get('is_admin', False) else "否"
        created_at = user_data.get('created_at', '')[:10] if user_data.get('created_at') else '-'
        
        print(f"{username:<15} {user_data.get('points', 0):<8} {user_data.get('total_generated', 0):<10} {is_admin:<8} {created_at}")

def main():
    """主函数"""
    print("梦羽AI绘图工具 - 系统初始化")
    print("="*50)
    
    # 创建管理员账户
    admin_created = create_admin_user()
    
    if not admin_created:
        print("管理员账户创建失败，退出")
        return
    
    # 询问是否创建测试用户
    while True:
        choice = input("\n是否创建测试用户？(y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            create_test_users()
            break
        elif choice in ['n', 'no']:
            break
        else:
            print("请输入 y 或 n")
    
    # 显示系统信息
    display_system_info()
    
    print("\n" + "="*50)
    print("初始化完成！")
    print("现在可以启动应用程序了：python app.py")
    print("="*50)

if __name__ == "__main__":
    main()
