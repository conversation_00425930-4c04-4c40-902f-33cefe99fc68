import json
import os
import random
import string
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import uuid


class RedemptionSystem:
    def __init__(self, data_file='redemption_codes.json'):
        self.data_file = data_file
        self.redemption_data = self.load_redemption_data()
    
    def load_redemption_data(self) -> Dict:
        """加载兑换码数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return self.create_default_data()
        return self.create_default_data()
    
    def create_default_data(self) -> Dict:
        """创建默认数据结构"""
        return {
            'codes': {},  # 兑换码数据
            'usage_records': [],  # 使用记录
            'statistics': {
                'total_codes_generated': 0,
                'total_codes_used': 0,
                'total_points_distributed': 0
            }
        }
    
    def save_redemption_data(self) -> bool:
        """保存兑换码数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.redemption_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def generate_code(self, length: int = 12) -> str:
        """生成随机兑换码"""
        # 使用大写字母和数字，避免容易混淆的字符
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        return ''.join(random.choice(chars) for _ in range(length))
    
    def create_redemption_codes(self, code_type: str, points: int, count: int = 1, 
                               expire_days: int = 30, description: str = '') -> List[str]:
        """
        创建兑换码
        
        Args:
            code_type: 兑换码类型 ('one_time' 一次性, 'activity' 活动码)
            points: 兑换积分数
            count: 生成数量
            expire_days: 过期天数
            description: 描述
        
        Returns:
            生成的兑换码列表
        """
        codes = []
        expire_time = datetime.now() + timedelta(days=expire_days)
        
        for _ in range(count):
            code = self.generate_code()
            # 确保兑换码唯一
            while code in self.redemption_data['codes']:
                code = self.generate_code()
            
            code_data = {
                'id': str(uuid.uuid4()),
                'code': code,
                'type': code_type,  # 'one_time' 或 'activity'
                'points': points,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'expire_at': expire_time.isoformat(),
                'is_active': True,
                'used_count': 0,
                'used_by': [],  # 使用过的用户列表（活动码用）
                'max_uses': 1 if code_type == 'one_time' else None  # 一次性码最多使用1次
            }
            
            self.redemption_data['codes'][code] = code_data
            codes.append(code)
        
        # 更新统计
        self.redemption_data['statistics']['total_codes_generated'] += count
        self.save_redemption_data()
        
        return codes
    
    def use_redemption_code(self, code: str, username: str) -> Tuple[bool, str, int]:
        """
        使用兑换码
        
        Returns:
            (成功状态, 消息, 获得积分)
        """
        if code not in self.redemption_data['codes']:
            return False, "兑换码不存在", 0
        
        code_data = self.redemption_data['codes'][code]
        
        # 检查兑换码是否激活
        if not code_data['is_active']:
            return False, "兑换码已被禁用", 0
        
        # 检查是否过期
        expire_time = datetime.fromisoformat(code_data['expire_at'])
        if datetime.now() > expire_time:
            return False, "兑换码已过期", 0
        
        # 根据兑换码类型进行不同的处理
        if code_data['type'] == 'one_time':
            # 一次性兑换码：使用后失效
            if code_data['used_count'] > 0:
                return False, "兑换码已被使用", 0
        elif code_data['type'] == 'activity':
            # 活动兑换码：每个用户只能使用一次
            if username in code_data['used_by']:
                return False, "您已经使用过这个兑换码", 0
        
        # 使用兑换码
        points = code_data['points']
        code_data['used_count'] += 1
        
        if code_data['type'] == 'one_time':
            # 一次性码使用后设为非激活状态
            code_data['is_active'] = False
        elif code_data['type'] == 'activity':
            # 活动码记录使用用户
            code_data['used_by'].append(username)
        
        # 记录使用记录
        usage_record = {
            'id': str(uuid.uuid4()),
            'code': code,
            'username': username,
            'points': points,
            'used_at': datetime.now().isoformat(),
            'code_type': code_data['type']
        }
        self.redemption_data['usage_records'].append(usage_record)
        
        # 更新统计
        self.redemption_data['statistics']['total_codes_used'] += 1
        self.redemption_data['statistics']['total_points_distributed'] += points
        
        self.save_redemption_data()
        
        return True, f"兑换成功！获得 {points} 积分", points
    
    def get_code_info(self, code: str) -> Optional[Dict]:
        """获取兑换码信息"""
        return self.redemption_data['codes'].get(code)
    
    def get_all_codes(self, include_inactive: bool = True) -> List[Dict]:
        """获取所有兑换码"""
        codes = []
        for code_data in self.redemption_data['codes'].values():
            if include_inactive or code_data['is_active']:
                codes.append(code_data.copy())
        
        # 按创建时间倒序排列
        codes.sort(key=lambda x: x['created_at'], reverse=True)
        return codes
    
    def get_usage_records(self, limit: int = 100) -> List[Dict]:
        """获取使用记录"""
        records = self.redemption_data['usage_records'].copy()
        records.sort(key=lambda x: x['used_at'], reverse=True)
        return records[:limit]
    
    def get_user_usage_records(self, username: str, limit: int = 50) -> List[Dict]:
        """获取用户的使用记录"""
        user_records = [
            record for record in self.redemption_data['usage_records']
            if record['username'] == username
        ]
        user_records.sort(key=lambda x: x['used_at'], reverse=True)
        return user_records[:limit]
    
    def deactivate_code(self, code: str) -> bool:
        """禁用兑换码"""
        if code in self.redemption_data['codes']:
            self.redemption_data['codes'][code]['is_active'] = False
            return self.save_redemption_data()
        return False
    
    def activate_code(self, code: str) -> bool:
        """激活兑换码"""
        if code in self.redemption_data['codes']:
            self.redemption_data['codes'][code]['is_active'] = True
            return self.save_redemption_data()
        return False
    
    def delete_code(self, code: str) -> bool:
        """删除兑换码"""
        if code in self.redemption_data['codes']:
            del self.redemption_data['codes'][code]
            return self.save_redemption_data()
        return False
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        stats = self.redemption_data['statistics'].copy()
        
        # 计算额外统计信息
        active_codes = sum(1 for code in self.redemption_data['codes'].values() if code['is_active'])
        expired_codes = 0
        now = datetime.now()
        
        for code_data in self.redemption_data['codes'].values():
            expire_time = datetime.fromisoformat(code_data['expire_at'])
            if now > expire_time:
                expired_codes += 1
        
        stats.update({
            'active_codes': active_codes,
            'expired_codes': expired_codes,
            'total_codes': len(self.redemption_data['codes'])
        })
        
        return stats
    
    def cleanup_expired_codes(self) -> int:
        """清理过期的兑换码"""
        now = datetime.now()
        expired_codes = []
        
        for code, code_data in self.redemption_data['codes'].items():
            expire_time = datetime.fromisoformat(code_data['expire_at'])
            if now > expire_time:
                expired_codes.append(code)
        
        # 删除过期的兑换码
        for code in expired_codes:
            del self.redemption_data['codes'][code]
        
        if expired_codes:
            self.save_redemption_data()
        
        return len(expired_codes)
