{"transactions": [{"id": 1, "username": "admin", "points_change": 990, "type": "admin_add", "description": "管理员操作: 管理员账户初始化", "timestamp": "2025-07-19T15:29:52.905737"}, {"id": 2, "username": "admin", "points_change": -1, "type": "spend", "description": "image生成: 1girl, solo, breasts, looking_at_viewer, blush, ba...", "timestamp": "2025-07-19T15:32:22.332361"}, {"id": 3, "username": "admin", "points_change": -1, "type": "spend", "description": "image生成: 1girl, solo, breasts, looking_at_viewer, blush, ba...", "timestamp": "2025-07-19T15:33:04.929661"}, {"id": 4, "username": "admin", "points_change": -1, "type": "spend", "description": "image生成: 1girl, solo, pantyhose, blue eyes, long hair, bow,...", "timestamp": "2025-07-19T15:54:47.236392"}, {"id": 5, "username": "admin", "points_change": -1, "type": "spend", "description": "image生成: 1girl, solo, breasts, looking_at_viewer, bangs, la...", "timestamp": "2025-07-19T16:13:00.312275"}, {"id": 6, "username": "admin", "points_change": -1, "type": "spend", "description": "image生成: 1girl, solo, breasts, long_sleeves, dress, ribbon,...", "timestamp": "2025-07-19T16:13:53.013026"}], "settings": {"image_generation_cost": 1, "video_generation_cost": 1, "new_user_bonus": 10, "daily_bonus": 5, "referral_bonus": 20, "global_proxy_api_url": "https://info.proxy.ipmars.com/extractProxyIp?regions=&num=1&protocol=http&return_type=txt&lh=1&st=", "require_registration_approval": true}, "statistics": {"total_transactions": 6, "total_points_issued": 990, "total_points_consumed": 5, "total_generations": 5}}