import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class PointsSystem:
    def __init__(self, data_file='points_data.json'):
        self.data_file = data_file
        self.points_data = self.load_points_data()
    
    def load_points_data(self) -> Dict:
        """加载积分数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError):
                return self.create_default_data()
        return self.create_default_data()
    
    def create_default_data(self) -> Dict:
        """创建默认数据结构"""
        return {
            'transactions': [],  # 积分交易记录
            'settings': {
                'image_generation_cost': 1,  # 图像生成消耗积分
                'video_generation_cost': 5,  # 视频生成消耗积分
                'new_user_bonus': 10,        # 新用户奖励积分
                'daily_bonus': 5,            # 每日签到奖励
                'referral_bonus': 20,        # 推荐奖励
                'global_proxy_api_url': '',  # 全局代理池API地址
                'require_registration_approval': False  # 是否需要管理员审核注册
            },
            'statistics': {
                'total_transactions': 0,
                'total_points_issued': 0,
                'total_points_consumed': 0,
                'total_generations': 0
            }
        }
    
    def save_points_data(self) -> bool:
        """保存积分数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.points_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError:
            return False
    
    def add_transaction(self, username: str, points_change: int, 
                       transaction_type: str, description: str = '') -> bool:
        """添加积分交易记录"""
        transaction = {
            'id': len(self.points_data['transactions']) + 1,
            'username': username,
            'points_change': points_change,
            'type': transaction_type,  # 'earn', 'spend', 'admin_add', 'admin_deduct'
            'description': description,
            'timestamp': datetime.now().isoformat()
        }
        
        self.points_data['transactions'].append(transaction)
        
        # 更新统计信息
        self.points_data['statistics']['total_transactions'] += 1
        if points_change > 0:
            self.points_data['statistics']['total_points_issued'] += points_change
        else:
            self.points_data['statistics']['total_points_consumed'] += abs(points_change)
        
        return self.save_points_data()
    
    def get_user_transactions(self, username: str, limit: int = 50) -> List[Dict]:
        """获取用户的积分交易记录"""
        user_transactions = [
            t for t in self.points_data['transactions'] 
            if t['username'] == username
        ]
        
        # 按时间倒序排列，返回最近的记录
        user_transactions.sort(key=lambda x: x['timestamp'], reverse=True)
        return user_transactions[:limit]
    
    def get_generation_cost(self, generation_type: str) -> int:
        """获取生成内容的积分消耗"""
        if generation_type == 'image':
            return self.points_data['settings']['image_generation_cost']
        elif generation_type == 'video':
            return self.points_data['settings']['video_generation_cost']
        return 1  # 默认消耗1积分
    
    def can_afford_generation(self, username: str, generation_type: str, 
                            user_points: int) -> Tuple[bool, str]:
        """检查用户是否有足够积分进行生成"""
        cost = self.get_generation_cost(generation_type)
        
        if user_points >= cost:
            return True, f"消耗{cost}积分"
        else:
            return False, f"积分不足，需要{cost}积分，当前只有{user_points}积分"
    
    def process_generation(self, username: str, generation_type: str, 
                          prompt: str, user_points: int) -> Tuple[bool, str, int]:
        """处理生成请求的积分扣除"""
        cost = self.get_generation_cost(generation_type)
        
        if user_points < cost:
            return False, f"积分不足，需要{cost}积分", user_points
        
        # 记录积分消耗
        description = f"{generation_type}生成: {prompt[:50]}..."
        self.add_transaction(username, -cost, 'spend', description)
        
        # 更新生成统计
        self.points_data['statistics']['total_generations'] += 1
        self.save_points_data()
        
        new_points = user_points - cost
        return True, f"生成成功，消耗{cost}积分，剩余{new_points}积分", new_points
    
    def admin_adjust_points(self, username: str, points_change: int, 
                           reason: str = '') -> bool:
        """管理员调整用户积分"""
        transaction_type = 'admin_add' if points_change > 0 else 'admin_deduct'
        description = f"管理员操作: {reason}" if reason else "管理员调整积分"
        
        return self.add_transaction(username, points_change, transaction_type, description)
    
    def get_system_statistics(self) -> Dict:
        """获取系统统计信息"""
        return self.points_data['statistics'].copy()
    
    def get_settings(self) -> Dict:
        """获取系统设置"""
        return self.points_data['settings'].copy()
    
    def update_settings(self, new_settings: Dict) -> bool:
        """更新系统设置"""
        self.points_data['settings'].update(new_settings)
        return self.save_points_data()
    
    def get_top_users_by_generation(self, limit: int = 10) -> List[Dict]:
        """获取生成次数最多的用户"""
        user_stats = {}
        
        for transaction in self.points_data['transactions']:
            if transaction['type'] == 'spend' and '生成' in transaction['description']:
                username = transaction['username']
                if username not in user_stats:
                    user_stats[username] = 0
                user_stats[username] += 1
        
        # 排序并返回前N名
        sorted_users = sorted(user_stats.items(), key=lambda x: x[1], reverse=True)
        return [{'username': username, 'generations': count} 
                for username, count in sorted_users[:limit]]
    
    def get_recent_activities(self, limit: int = 20) -> List[Dict]:
        """获取最近的积分活动"""
        recent_transactions = sorted(
            self.points_data['transactions'], 
            key=lambda x: x['timestamp'], 
            reverse=True
        )
        return recent_transactions[:limit]
    
    def cleanup_old_transactions(self, days_to_keep: int = 90) -> int:
        """清理旧的交易记录，保留指定天数内的记录"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_iso = cutoff_date.isoformat()
        
        original_count = len(self.points_data['transactions'])
        
        # 保留较新的交易记录
        self.points_data['transactions'] = [
            t for t in self.points_data['transactions']
            if t['timestamp'] > cutoff_iso
        ]
        
        cleaned_count = original_count - len(self.points_data['transactions'])
        
        if cleaned_count > 0:
            self.save_points_data()
        
        return cleaned_count
